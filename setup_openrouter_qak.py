#!/usr/bin/env python3
"""
QAK OpenRouter Integration Script
Configures OpenRouter with gpt-4o-mini model for browser-use automation in QAK.
"""

import os
import asyncio
import logging
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_openrouter_environment():
    """Setup OpenRouter environment variables for QAK."""
    
    # Check if OpenRouter API key is set
    if not os.getenv("OPENROUTER_API_KEY"):
        logger.error("❌ OPENROUTER_API_KEY not found in environment variables")
        logger.info("To set up OpenRouter:")
        logger.info("1. Get your API key from https://openrouter.ai/keys")
        logger.info("2. Add to your .env file: OPENROUTER_API_KEY=your-api-key-here")
        logger.info("3. Or export it: export OPENROUTER_API_KEY='your-api-key-here'")
        return False
    
    # Set default model for browser-use
    os.environ.setdefault("BROWSER_USE_MODEL_PROVIDER", "openrouter")
    os.environ.setdefault("BROWSER_USE_MODEL_NAME", "openai/gpt-4o-mini")
    
    logger.info("✅ OpenRouter environment configured")
    logger.info(f"✅ Model: {os.getenv('BROWSER_USE_MODEL_NAME', 'openai/gpt-4o-mini')}")
    return True

def create_openrouter_config() -> Dict[str, Any]:
    """Create BrowserHelperConfig for OpenRouter."""
    
    from src.utilities.browser_helper import BrowserHelperConfig
    
    config = BrowserHelperConfig(
        # OpenRouter LLM settings
        model_provider="openrouter",
        model_name="openai/gpt-4o-mini",
        temperature=0.0,
        
        # Browser settings optimized for testing
        headless=True,
        use_vision=True,
        disable_security=True,
        deterministic_rendering=True,
        
        # Agent settings
        max_steps=15,
        max_failures=3,
        retry_delay=2.0,
        
        # Performance settings
        minimum_wait_page_load_time=1.0,
        wait_for_network_idle_page_load_time=2.0,
        maximum_wait_page_load_time=30.0,
        wait_between_actions=0.5,
        
        # Viewport settings
        viewport={"width": 1280, "height": 720},
        
        # Language settings
        response_language="es",  # Spanish responses for QAK
        
        # Recording settings (optional)
        generate_gif=False,
        save_conversation_path=None
    )
    
    logger.info("✅ OpenRouter configuration created")
    return config

async def test_openrouter_integration():
    """Test OpenRouter integration with QAK browser automation."""
    
    if not setup_openrouter_environment():
        return False
    
    try:
        # Import QAK components
        from src.utilities.browser_helper import create_llm_instance, create_and_run_agent
        
        # Create configuration
        config = create_openrouter_config()
        
        # Create LLM instance
        logger.info("🔧 Creating OpenRouter LLM instance...")
        llm = create_llm_instance(config, os.getenv("OPENROUTER_API_KEY"))
        
        # Test basic functionality
        logger.info("🧪 Testing LLM basic functionality...")
        if hasattr(llm, 'model'):
            logger.info(f"✅ Model: {llm.model}")
        if hasattr(llm, 'base_url'):
            logger.info(f"✅ Base URL: {llm.base_url}")
        
        # Create a simple test scenario
        test_scenario = """
        Feature: OpenRouter Integration Test
        Scenario: Navigate to test page
          Given I am on the internet
          When I navigate to "https://httpbin.org/get"
          Then I should see the page has loaded successfully
          And I should see JSON response data
        """
        
        logger.info("🚀 Running browser automation test with OpenRouter...")
        
        # Run the test
        result = await create_and_run_agent(
            scenario_text=test_scenario,
            controller_instance=None,  # Will be created automatically
            api_key=os.getenv("OPENROUTER_API_KEY"),
            language="es",
            config=config,
            url="https://httpbin.org/get"
        )
        
        if result and hasattr(result, 'is_successful') and result.is_successful():
            logger.info("✅ OpenRouter integration test completed successfully!")
            logger.info(f"📊 Total steps executed: {len(result.history) if hasattr(result, 'history') else 'N/A'}")
            
            # Show final result
            if hasattr(result, 'final_result') and result.final_result():
                logger.info(f"📄 Final result: {result.final_result()}")
                
            return True
        else:
            logger.warning("⚠️ OpenRouter integration test completed with issues")
            return False
            
    except Exception as e:
        logger.error(f"❌ OpenRouter integration test failed: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        return False

def create_qak_test_execution():
    """Create a test execution example for QAK with OpenRouter."""
    
    example_code = '''
# Example: Using OpenRouter with QAK Test Execution

from src.utilities.browser_helper import BrowserHelperConfig, create_and_run_agent
import asyncio

async def run_qak_test_with_openrouter():
    """Run a QAK test case using OpenRouter."""
    
    # Configure OpenRouter
    config = BrowserHelperConfig(
        model_provider="openrouter",
        model_name="openai/gpt-4o-mini",
        temperature=0.0,
        headless=True,
        use_vision=True,
        max_steps=10,
        response_language="es"
    )
    
    # Your test scenario
    scenario = """
    Feature: Login Test
    Scenario: User login validation
      Given I am on the login page
      When I enter valid credentials
      Then I should be logged in successfully
    """
    
    # Run the test
    result = await create_and_run_agent(
        scenario_text=scenario,
        controller_instance=None,
        config=config,
        url="https://your-app.com/login"
    )
    
    return result

# Run the test
if __name__ == "__main__":
    result = asyncio.run(run_qak_test_with_openrouter())
    print(f"Test completed: {'Success' if result.is_successful() else 'Failed'}")
    '''
    
    # Save example to file
    with open('/Users/<USER>/Proyectos/qak/examples/openrouter_example.py', 'w') as f:
        f.write(example_code)
    
    logger.info("✅ Example file created: examples/openrouter_example.py")

async def main():
    """Main execution function."""
    
    print("🚀 QAK OpenRouter Integration Setup")
    print("=" * 50)
    
    # Setup environment
    if not setup_openrouter_environment():
        print("❌ Environment setup failed. Please configure OPENROUTER_API_KEY.")
        return
    
    # Test integration
    print("\n🧪 Testing OpenRouter Integration...")
    success = await test_openrouter_integration()
    
    if success:
        print("\n✅ OpenRouter integration successful!")
        print("\n📝 Configuration Summary:")
        print(f"   • Provider: OpenRouter")
        print(f"   • Model: openai/gpt-4o-mini")
        print(f"   • Base URL: https://openrouter.ai/api/v1")
        print(f"   • Vision: Enabled")
        print(f"   • Language: Spanish responses")
        
        # Create example
        os.makedirs('/Users/<USER>/Proyectos/qak/examples', exist_ok=True)
        create_qak_test_execution()
        
        print("\n🎯 Next Steps:")
        print("1. Use OpenRouter in your QAK test executions")
        print("2. Check the example file: examples/openrouter_example.py")
        print("3. Monitor costs at https://openrouter.ai/activity")
        
    else:
        print("\n❌ OpenRouter integration failed. Check logs for details.")

if __name__ == "__main__":
    asyncio.run(main())
