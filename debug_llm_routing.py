#!/usr/bin/env python3
"""Debug script to understand LLM routing issues."""

import os
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utilities.browser_helper import BrowserHelperConfig, create_llm_instance

def debug_llm_routing():
    """Debug LLM routing with OpenRouter configuration."""
    
    # Check environment variables
    print("Environment variables:")
    print(f"OPENROUTER_API_KEY: {'SET' if os.getenv('OPENROUTER_API_KEY') else 'NOT SET'}")
    print(f"GOOGLE_API_KEY: {'SET' if os.getenv('GOOGLE_API_KEY') else 'NOT SET'}")
    print()
    
    # Create OpenRouter config
    config = BrowserHelperConfig(
        model_provider="openrouter",
        model_name="openai/gpt-4o-mini",
        temperature=0.0
    )
    
    print(f"Config created:")
    print(f"  model_provider: {config.model_provider}")
    print(f"  model_name: {config.model_name}")
    print()
    
    # Test the routing logic
    api_key = os.getenv("OPENROUTER_API_KEY")
    
    if not api_key:
        print("ERROR: OPENROUTER_API_KEY not found in environment")
        return
    
    print(f"Using API key: {api_key[:10]}...")
    print()
    
    # Test the create_llm_instance function
    try:
        print("Testing create_llm_instance...")
        llm_instance = create_llm_instance(config, api_key)
        print(f"Success! LLM instance created: {type(llm_instance)}")
        print(f"LLM instance attributes:")
        if hasattr(llm_instance, 'model'):
            print(f"  model: {llm_instance.model}")
        if hasattr(llm_instance, 'base_url'):
            print(f"  base_url: {llm_instance.base_url}")
        if hasattr(llm_instance, 'api_key'):
            print(f"  api_key: {llm_instance.api_key[:10]}...")
        print()
        
    except Exception as e:
        print(f"ERROR creating LLM instance: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_llm_routing()
