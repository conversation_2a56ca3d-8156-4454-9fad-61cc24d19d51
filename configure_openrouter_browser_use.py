#!/usr/bin/env python3
"""
Configure OpenRouter for browser-use in QAK system.
This script demonstrates how to use OpenRouter with browser-use automation.
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from src.utilities.browser_helper import BrowserHelperConfig, create_llm_instance

async def test_openrouter_browser_use():
    """Test OpenRouter integration with browser-use."""
    
    # Check if OpenRouter API key is configured
    if not os.getenv("OPENROUTER_API_KEY"):
        logger.error("❌ OPENROUTER_API_KEY not found in environment variables")
        logger.info("Please set your OpenRouter API key:")
        logger.info("export OPENROUTER_API_KEY='your-api-key-here'")
        return False
    
    try:
        # Create configuration for OpenRouter
        config = BrowserHelperConfig(
            model_provider="openrouter",
            model_name="openai/gpt-4o-mini",  # OpenRouter model format
            temperature=0.0,
            headless=True,
            use_vision=True,
            max_steps=10,
            max_failures=3
        )
        
        # Create LLM instance
        logger.info("🔧 Creating OpenRouter LLM instance...")
        llm = create_llm_instance(config, os.getenv("OPENROUTER_API_KEY"))
        
        # Test basic LLM functionality
        logger.info("🧪 Testing LLM instance...")
        if hasattr(llm, 'model'):
            logger.info(f"✅ LLM model: {llm.model}")
        if hasattr(llm, 'base_url'):
            logger.info(f"✅ LLM base_url: {llm.base_url}")
            
        # Test with structured output (like browser-use does)
        from browser_use.controller.registry.views import ActionModel
        from browser_use.agent.views import AgentOutput
        
        logger.info("🔍 Testing structured output...")
        
        # Create a simple test schema
        from pydantic import BaseModel
        from typing import List, Optional
        
        class TestAction(BaseModel):
            done: Optional[dict] = None
            
        class TestOutput(BaseModel):
            action: List[TestAction]
            
        # Test structured output
        structured_llm = llm.with_structured_output(TestOutput)
        
        test_messages = [
            {"role": "user", "content": "Please respond with a simple done action indicating success."}
        ]
        
        logger.info("🚀 Testing structured LLM call...")
        result = await structured_llm.ainvoke(test_messages)
        
        if result and hasattr(result, 'action'):
            logger.info("✅ OpenRouter structured output test successful!")
            logger.info(f"✅ Response: {result}")
            return True
        else:
            logger.error("❌ OpenRouter structured output test failed - no action in response")
            return False
            
    except Exception as e:
        logger.error(f"❌ OpenRouter test failed: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        return False

async def example_browser_automation():
    """Example of using OpenRouter with browser-use for automation."""
    
    if not os.getenv("OPENROUTER_API_KEY"):
        logger.error("❌ OPENROUTER_API_KEY required for browser automation")
        return
    
    try:
        from browser_use.agent.service import Agent
        from browser_use.browser.session import BrowserSession
        
        # Create configuration
        config = BrowserHelperConfig(
            model_provider="openrouter",
            model_name="openai/gpt-4o-mini",
            temperature=0.0,
            headless=True,
            use_vision=True,
            max_steps=5
        )
        
        # Create LLM
        llm = create_llm_instance(config, os.getenv("OPENROUTER_API_KEY"))
        
        logger.info("🤖 Creating browser-use agent with OpenRouter...")
        
        # Create agent
        agent = Agent(
            task="Navigate to https://httpbin.org/get and verify the page loads successfully",
            llm=llm,
            use_vision=True,
            max_actions_per_step=3,
            max_failures=2
        )
        
        logger.info("🚀 Running browser automation with OpenRouter...")
        
        # Run the automation
        result = await agent.run(max_steps=5)
        
        if result.is_successful():
            logger.info("✅ Browser automation completed successfully!")
        else:
            logger.info("⚠️ Browser automation completed with issues")
            
        # Display results
        logger.info(f"📊 Total steps: {len(result.history)}")
        if result.final_result():
            logger.info(f"📄 Final result: {result.final_result()}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Browser automation failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 OpenRouter Browser-Use Configuration Test")
    print("=" * 50)
    
    asyncio.run(test_openrouter_browser_use())
    
    print("\n🤖 Browser Automation Example")
    print("=" * 50)
    
    asyncio.run(example_browser_automation())
