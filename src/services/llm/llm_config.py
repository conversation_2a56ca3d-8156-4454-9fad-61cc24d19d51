"""
LLM Configuration - Centralized configuration for all LLM services
"""

import os
from typing import Dict, Any, List


class LLMConfig:
    """Central configuration for LLM services."""
    
    # Provider availability
    GEMINI_ENABLED = bool(os.getenv("GOOGLE_API_KEY"))
    OPENROUTER_ENABLED = bool(os.getenv("OPENROUTER_API_KEY"))
    
    # Feature flags for gradual migration (all enabled by default for OpenRouter as primary)
    USE_OPENROUTER_FOR_GHERKIN = os.getenv("USE_OPENROUTER_FOR_GHERKIN", "true").lower() == "true"
    USE_OPENROUTER_FOR_VALIDATION = os.getenv("USE_OPENROUTER_FOR_VALIDATION", "true").lower() == "true"
    USE_OPENROUTER_FOR_TRANSLATION = os.getenv("USE_OPENROUTER_FOR_TRANSLATION", "true").lower() == "true"
    USE_OPENROUTER_FOR_ENHANCEMENT = os.getenv("USE_OPENROUTER_FOR_ENHANCEMENT", "true").lower() == "true"
    
    # Default provider preferences by use case
    PROVIDER_PREFERENCES: Dict[str, List[str]] = {
        "gherkin": ["openrouter", "gemini"] if USE_OPENROUTER_FOR_GHERKIN else ["gemini", "openrouter"],
        "validation": ["openrouter", "gemini"] if USE_OPENROUTER_FOR_VALIDATION else ["gemini", "openrouter"],
        "translation": ["openrouter", "gemini"] if USE_OPENROUTER_FOR_TRANSLATION else ["gemini", "openrouter"],
        "enhancement": ["openrouter", "gemini"] if USE_OPENROUTER_FOR_ENHANCEMENT else ["gemini", "openrouter"],
        "test_analysis": ["openrouter", "gemini"],  # Test analysis with step validation and completion analysis
        "script_generation": ["openrouter", "gemini"],  # AI-powered automation script generation
        "browser_automation": ["gemini", "openrouter"],  # Keep Gemini first for browser work
        "general": ["openrouter", "gemini"]  # OpenRouter as default primary provider
    }
    
    # Cost optimization settings
    OPENROUTER_PREFER_FREE_MODELS = os.getenv("OPENROUTER_PREFER_FREE_MODELS", "true").lower() == "true"
    OPENROUTER_MAX_COST_PER_1K_TOKENS = float(os.getenv("OPENROUTER_MAX_COST_PER_1K_TOKENS", "0.01"))
    
    # Rate limiting
    RESPECT_RATE_LIMITS = os.getenv("RESPECT_RATE_LIMITS", "true").lower() == "true"
    RATE_LIMIT_BUFFER = float(os.getenv("RATE_LIMIT_BUFFER", "0.2"))
    
    # Fallback behavior
    ENABLE_PROVIDER_FALLBACK = os.getenv("ENABLE_PROVIDER_FALLBACK", "true").lower() == "true"
    MAX_RETRIES_PER_PROVIDER = int(os.getenv("MAX_RETRIES_PER_PROVIDER", "3"))
    
    @classmethod
    def get_provider_order(cls, use_case: str) -> List[str]:
        """Get provider order for a specific use case."""
        return cls.PROVIDER_PREFERENCES.get(use_case, cls.PROVIDER_PREFERENCES["general"])
    
    @classmethod
    def get_status(cls) -> Dict[str, Any]:
        """Get current configuration status."""
        return {
            "providers": {
                "gemini": {
                    "enabled": cls.GEMINI_ENABLED,
                    "api_key_configured": bool(os.getenv("GOOGLE_API_KEY"))
                },
                "openrouter": {
                    "enabled": cls.OPENROUTER_ENABLED,
                    "api_key_configured": bool(os.getenv("OPENROUTER_API_KEY"))
                }
            },
            "feature_flags": {
                "use_openrouter_for_gherkin": cls.USE_OPENROUTER_FOR_GHERKIN,
                "use_openrouter_for_validation": cls.USE_OPENROUTER_FOR_VALIDATION,
                "use_openrouter_for_translation": cls.USE_OPENROUTER_FOR_TRANSLATION,
                "use_openrouter_for_enhancement": cls.USE_OPENROUTER_FOR_ENHANCEMENT
            },
            "settings": {
                "prefer_free_models": cls.OPENROUTER_PREFER_FREE_MODELS,
                "max_cost_per_1k_tokens": cls.OPENROUTER_MAX_COST_PER_1K_TOKENS,
                "respect_rate_limits": cls.RESPECT_RATE_LIMITS,
                "enable_fallback": cls.ENABLE_PROVIDER_FALLBACK
            },
            "provider_preferences": cls.PROVIDER_PREFERENCES
        }
