import logging
"""
Rutas API para gestión de configuraciones de pruebas.
"""

import os
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse

from src.api.models import (
    TestConfigurationRequest,
    TestConfigurationResponse,
    TestConfigurationCreateRequest,
    TestConfigurationUpdateRequest,
    PredefinedConfigResponse
)

from src.config.browser_config import (
    BrowserConfigurations,
    get_config_by_type,
    BrowserHelperConfig
)

from src.utilities.browser_helper import (
    validate_config,
    create_fast_config,
    create_robust_config,
    create_secure_config,
    create_debug_config
)

router = APIRouter(tags=["Configuration"])

# Directorio para almacenar configuraciones personalizadas
CONFIG_DIR = Path("config/custom")
CONFIG_DIR.mkdir(parents=True, exist_ok=True)


def _get_env_defaults() -> Dict[str, Any]:
    """Obtiene los valores por defecto desde las variables de entorno."""
    from src.config.browser_config import BrowserConfigurations

    return {
        "model_name": os.getenv("LLM_MODEL", "gemini-2.5-flash"),
        "embedder_model": os.getenv("EMBEDDING_MODEL", "models/text-embedding-004"),
        "embedder_provider": os.getenv("EMBEDDING_PROVIDER", "gemini"),
        "embedder_dims": int(os.getenv("EMBEDDING_DIMS", "768")),
        "model_provider": BrowserConfigurations.get_model_provider_from_env(),
        "temperature": 0.1,
        "memory_agent_id": "browser_use_agent",
    }


def _apply_env_defaults(config_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Aplica valores por defecto del .env a una configuración si no están presentes."""
    env_defaults = _get_env_defaults()
    
    # Aplicar defaults solo si el valor no existe o es None
    for key, default_value in env_defaults.items():
        if key not in config_dict or config_dict[key] is None:
            config_dict[key] = default_value
    
    return config_dict


def _browser_helper_config_to_dict(config: BrowserHelperConfig) -> Dict[str, Any]:
    """Convierte una instancia de BrowserHelperConfig a diccionario."""
    return {
        # Configuración del navegador
        "headless": config.headless,
        "user_data_dir": config.user_data_dir,
        "allowed_domains": config.allowed_domains,
        "disable_security": config.disable_security,
        "deterministic_rendering": config.deterministic_rendering,
        "highlight_elements": config.highlight_elements,
        "viewport_expansion": config.viewport_expansion,
        
        # Configuración de rendimiento
        "minimum_wait_page_load_time": config.minimum_wait_page_load_time,
        "wait_for_network_idle_page_load_time": config.wait_for_network_idle_page_load_time,
        "maximum_wait_page_load_time": config.maximum_wait_page_load_time,
        "wait_between_actions": config.wait_between_actions,
        
        # Configuración del agente
        "max_steps": config.max_steps,
        "max_failures": config.max_failures,
        "retry_delay": config.retry_delay,
        "use_vision": config.use_vision,
        "enable_memory": config.enable_memory,
        "save_conversation_path": config.save_conversation_path,
        "generate_gif": config.generate_gif,
        
        # Configuración de memoria
        "memory_agent_id": config.memory_agent_id,
        "memory_interval": config.memory_interval,
        
        # Configuración de embeddings
        "embedder_provider": config.embedder_provider,
        "embedder_model": config.embedder_model,
        "embedder_dims": config.embedder_dims,
        "vector_store_provider": config.vector_store_provider,
        "vector_store_base_path": config.vector_store_base_path,
        
        # Configuración de planificador
        "planner_llm": str(config.planner_llm) if config.planner_llm else None,
        "use_vision_for_planner": config.use_vision_for_planner,
        "planner_interval": config.planner_interval,
        
        # Configuración inicial
        "initial_actions": config.initial_actions,
        "keep_alive": config.keep_alive,
        "storage_state": config.storage_state,
        
        # Configuración del modelo
        "model_provider": config.model_provider,
        "model_name": config.model_name,
        "temperature": config.temperature,
        
        # Configuración del viewport
        "viewport": config.viewport,
        "device_scale_factor": config.device_scale_factor,
        
        # Configuración de grabación
        "record_video_dir": config.record_video_dir,
        "trace_path": config.trace_path
    }


def _dict_to_browser_helper_config(config_dict: Dict[str, Any]) -> BrowserHelperConfig:
    """Convierte un diccionario a una instancia de BrowserHelperConfig."""
    # Filtrar valores None del diccionario, EXCEPTO user_data_dir que puede ser None explícitamente
    filtered_dict = {}
    for k, v in config_dict.items():
        if k == "user_data_dir":
            # Permitir que user_data_dir sea None explícitamente
            filtered_dict[k] = v
        elif v is not None:
            # Para otros campos, filtrar valores None
            filtered_dict[k] = v
    
    return BrowserHelperConfig(**filtered_dict)


def _load_custom_config(config_id: str) -> Optional[Dict[str, Any]]:
    """Carga una configuración personalizada desde el archivo."""
    config_file = CONFIG_DIR / f"{config_id}.json"
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None


def _save_custom_config(config_id: str, config_data: Dict[str, Any]) -> None:
    """Guarda una configuración personalizada en el archivo."""
    config_file = CONFIG_DIR / f"{config_id}.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)


def _list_custom_configs() -> List[Dict[str, Any]]:
    """Lista todas las configuraciones personalizadas."""
    configs = []
    for config_file in CONFIG_DIR.glob("*.json"):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                # Aplicar defaults del .env a la configuración
                if 'settings' in config_data:
                    config_data['settings'] = _apply_env_defaults(config_data['settings'])
                configs.append(config_data)
        except Exception as e:
            logging.info(f"Error cargando configuración {config_file}: {e}")
    return configs


@router.get("/predefined", response_model=List[PredefinedConfigResponse], summary="Obtener configuraciones predefinidas")
async def get_predefined_configurations():
    """
    Obtiene todas las configuraciones predefinidas disponibles.
    
    Returns:
        Lista de configuraciones predefinidas con sus detalles
    """
    predefined_configs = [
        {
            "config_type": "ci",
            "name": "CI/CD",
            "description": "Configuración optimizada para CI/CD - velocidad máxima",
            "settings": _apply_env_defaults(_browser_helper_config_to_dict(BrowserConfigurations.get_ci_cd_config()))
        },
        {
            "config_type": "smoke",
            "name": "Smoke Test",
            "description": "Configuración para smoke tests - balance entre velocidad y confiabilidad",
            "settings": _apply_env_defaults(_browser_helper_config_to_dict(BrowserConfigurations.get_smoke_test_config()))
        },
        {
            "config_type": "regression",
            "name": "Regression Test",
            "description": "Configuración para tests de regresión - máxima confiabilidad",
            "settings": _apply_env_defaults(_browser_helper_config_to_dict(BrowserConfigurations.get_regression_test_config()))
        },
        {
            "config_type": "dev",
            "name": "Development",
            "description": "Configuración para desarrollo - debugging y observación",
            "settings": _apply_env_defaults(_browser_helper_config_to_dict(BrowserConfigurations.get_development_config()))
        },
        {
            "config_type": "web",
            "name": "Web Interface",
            "description": "Configuración para interfaz web - balance para usuarios",
            "settings": _apply_env_defaults(_browser_helper_config_to_dict(BrowserConfigurations.get_web_interface_config()))
        },
        {
            "config_type": "api",
            "name": "API Testing",
            "description": "Configuración para testing de APIs - enfoque en funcionalidad",
            "settings": _apply_env_defaults(_browser_helper_config_to_dict(BrowserConfigurations.get_api_testing_config()))
        },
        {
            "config_type": "load",
            "name": "Load Testing",
            "description": "Configuración para load testing - mínimo overhead",
            "settings": _apply_env_defaults(_browser_helper_config_to_dict(BrowserConfigurations.get_load_testing_config()))
        },
        {
            "config_type": "test_suite",
            "name": "Test Suite",
            "description": "Configuración especializada para ejecución de suites completas - evita límites de cuota API",
            "settings": _apply_env_defaults(_browser_helper_config_to_dict(BrowserConfigurations.get_test_suite_config()))
        }
    ]
    
    return predefined_configs


@router.get("/predefined/{config_type}", response_model=PredefinedConfigResponse, summary="Obtener configuración predefinida específica")
async def get_predefined_configuration(config_type: str):
    """
    Obtiene una configuración predefinida específica.
    
    Args:
        config_type: Tipo de configuración (ci, smoke, regression, dev, web, api, load, test_suite)
        
    Returns:
        Configuración predefinida solicitada
    """
    try:
        config = get_config_by_type(config_type)
        
        config_names = {
            "ci": "CI/CD",
            "smoke": "Smoke Test",
            "regression": "Regression Test",
            "dev": "Development",
            "web": "Web Interface",
            "api": "API Testing",
            "load": "Load Testing",
            "test_suite": "Test Suite"
        }
        
        config_descriptions = {
            "ci": "Configuración optimizada para CI/CD - velocidad máxima",
            "smoke": "Configuración para smoke tests - balance entre velocidad y confiabilidad",
            "regression": "Configuración para tests de regresión - máxima confiabilidad",
            "dev": "Configuración para desarrollo - debugging y observación",
            "web": "Configuración para interfaz web - balance para usuarios",
            "api": "Configuración para testing de APIs - enfoque en funcionalidad",
            "load": "Configuración para load testing - mínimo overhead",
            "test_suite": "Configuración especializada para ejecución de suites completas - evita límites de cuota API"
        }
        
        return PredefinedConfigResponse(
            config_type=config_type,
            name=config_names.get(config_type, config_type.title()),
            description=config_descriptions.get(config_type, f"Configuración {config_type}"),
            settings=_browser_helper_config_to_dict(config)
        )
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Configuración {config_type} no encontrada: {str(e)}")


@router.post("/validate", summary="Validar configuración")
async def validate_configuration(config_request: TestConfigurationRequest):
    """
    Valida una configuración de pruebas.
    
    Args:
        config_request: Configuración a validar
        
    Returns:
        Resultado de la validación con advertencias si las hay
    """
    try:
        # Convertir la request a diccionario y crear BrowserHelperConfig
        config_dict = config_request.dict(exclude_none=True)
        browser_config = _dict_to_browser_helper_config(config_dict)
        
        # Validar la configuración
        warnings = validate_config(browser_config)
        
        return {
            "valid": len(warnings) == 0,
            "warnings": warnings,
            "message": "Configuración válida" if len(warnings) == 0 else "Configuración con advertencias"
        }
    except Exception as e:
        return {
            "valid": False,
            "warnings": [],
            "message": f"Error en la configuración: {str(e)}"
        }


@router.get("/custom", response_model=List[TestConfigurationResponse], summary="Obtener configuraciones personalizadas")
async def get_custom_configurations():
    """
    Obtiene todas las configuraciones personalizadas guardadas.
    
    Returns:
        Lista de configuraciones personalizadas
    """
    configs = _list_custom_configs()
    return [TestConfigurationResponse(**config) for config in configs]


@router.post("/custom", response_model=TestConfigurationResponse, summary="Crear configuración personalizada")
async def create_custom_configuration(config_create: TestConfigurationCreateRequest):
    """
    Crea una nueva configuración personalizada.
    
    Args:
        config_create: Datos de la nueva configuración
        
    Returns:
        Configuración creada
    """
    try:
        # Generar ID único
        config_id = str(uuid.uuid4())
        
        # Validar la configuración
        config_dict = config_create.settings.dict(exclude_none=True)
        # Aplicar defaults del .env
        config_dict = _apply_env_defaults(config_dict)
        browser_config = _dict_to_browser_helper_config(config_dict)
        warnings = validate_config(browser_config)
        
        # Preparar datos de la configuración
        now = datetime.now().isoformat()
        config_data = {
            "config_id": config_id,
            "name": config_create.name,
            "description": config_create.description,
            "config_type": config_create.config_type,
            "settings": config_dict,
            "created_at": now,
            "updated_at": now,
            "is_default": config_create.is_default
        }
        
        # Si hay advertencias, incluirlas en la respuesta pero permitir guardar
        if warnings:
            config_data["validation_warnings"] = warnings
        
        # Guardar la configuración
        _save_custom_config(config_id, config_data)
        
        return TestConfigurationResponse(**config_data)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error creando configuración: {str(e)}")


@router.get("/custom/{config_id}", response_model=TestConfigurationResponse, summary="Obtener configuración personalizada")
async def get_custom_configuration(config_id: str):
    """
    Obtiene una configuración personalizada específica.
    
    Args:
        config_id: ID de la configuración
        
    Returns:
        Configuración solicitada
    """
    config_data = _load_custom_config(config_id)
    if not config_data:
        raise HTTPException(status_code=404, detail="Configuración no encontrada")
    
    # Aplicar defaults del .env
    if 'settings' in config_data:
        config_data['settings'] = _apply_env_defaults(config_data['settings'])
    
    return TestConfigurationResponse(**config_data)


@router.put("/custom/{config_id}", response_model=TestConfigurationResponse, summary="Actualizar configuración personalizada")
async def update_custom_configuration(config_id: str, config_update: TestConfigurationUpdateRequest):
    """
    Actualiza una configuración personalizada existente.
    
    Args:
        config_id: ID de la configuración
        config_update: Datos actualizados
        
    Returns:
        Configuración actualizada
    """
    # Cargar configuración existente
    config_data = _load_custom_config(config_id)
    if not config_data:
        raise HTTPException(status_code=404, detail="Configuración no encontrada")
    
    try:
        # Actualizar solo los campos proporcionados
        update_dict = config_update.dict(exclude_none=True)
        
        if "name" in update_dict:
            config_data["name"] = update_dict["name"]
        if "description" in update_dict:
            config_data["description"] = update_dict["description"]
        if "config_type" in update_dict:
            config_data["config_type"] = update_dict["config_type"]
        if "is_default" in update_dict:
            config_data["is_default"] = update_dict["is_default"]
        if "settings" in update_dict:
            # Validar nueva configuración
            new_settings = update_dict["settings"]
            # Aplicar defaults del .env
            new_settings = _apply_env_defaults(new_settings)
            browser_config = _dict_to_browser_helper_config(new_settings)
            warnings = validate_config(browser_config)
            
            config_data["settings"] = new_settings
            if warnings:
                config_data["validation_warnings"] = warnings
            else:
                config_data.pop("validation_warnings", None)
        
        config_data["updated_at"] = datetime.now().isoformat()
        
        # Guardar configuración actualizada
        _save_custom_config(config_id, config_data)
        
        return TestConfigurationResponse(**config_data)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error actualizando configuración: {str(e)}")


@router.delete("/custom/{config_id}", summary="Eliminar configuración personalizada")
async def delete_custom_configuration(config_id: str):
    """
    Elimina una configuración personalizada.
    
    Args:
        config_id: ID de la configuración
        
    Returns:
        Confirmación de eliminación
    """
    config_file = CONFIG_DIR / f"{config_id}.json"
    if not config_file.exists():
        raise HTTPException(status_code=404, detail="Configuración no encontrada")
    
    try:
        config_file.unlink()
        return {"message": "Configuración eliminada exitosamente", "config_id": config_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error eliminando configuración: {str(e)}")


@router.post("/test", summary="Probar configuración")
async def test_configuration(config_request: TestConfigurationRequest):
    """
    Prueba una configuración creando una instancia de BrowserHelperConfig y validándola.
    
    Args:
        config_request: Configuración a probar
        
    Returns:
        Resultado de la prueba con detalles de la configuración aplicada
    """
    try:
        # Convertir la request a diccionario y crear BrowserHelperConfig
        config_dict = config_request.dict(exclude_none=True)
        browser_config = _dict_to_browser_helper_config(config_dict)
        
        # Validar la configuración
        warnings = validate_config(browser_config)
        
        # Obtener la configuración final aplicada
        final_config = _browser_helper_config_to_dict(browser_config)
        
        return {
            "success": True,
            "warnings": warnings,
            "final_config": final_config,
            "message": "Configuración probada exitosamente"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Error probando configuración"
        }


@router.get("/", summary="Obtener todas las configuraciones")
async def get_all_configurations():
    """
    Obtiene todas las configuraciones disponibles (predefinidas y personalizadas).
    
    Returns:
        Diccionario con configuraciones predefinidas y personalizadas
    """
    # Obtener configuraciones predefinidas
    predefined = await get_predefined_configurations()
    
    # Obtener configuraciones personalizadas
    custom = await get_custom_configurations()
    
    return {
        "predefined": predefined,
        "custom": custom
    }


@router.get("/defaults", summary="Obtener valores por defecto del entorno")
async def get_environment_defaults():
    """
    Obtiene los valores por defecto desde las variables de entorno.
    Útil para inicializar formularios con valores predeterminados.
    
    Returns:
        Diccionario con valores por defecto del .env
    """
    return _get_env_defaults()
